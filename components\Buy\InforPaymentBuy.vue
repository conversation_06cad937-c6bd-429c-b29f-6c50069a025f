<template>
  <div class="px-1 bg-white rounded flex flex-col gap-1">
    <div class="flex items-center gap-1 px-1">
      <span class="font-bold text-primary">Thông tin thanh toán</span>
    </div>
    <div>
      <div
        v-for="voucher in vouchers"
        class="flex items-center justify-between px-1"
      >
        <div class="flex items-center gap-1">
          <div
            @click="handleRemoveVoucher(voucher)"
            class="text-red-500 cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </div>
          <div>{{ voucher.voucherCode || voucher.title }}</div>
        </div>
        <div class="text-primary">
          {{
            ` - ${formatCurrency(voucher.value.amount)} ${
              voucher.percent ? `(${voucher.percent})%` : " "
            }`
          }}
        </div>
      </div>

      <div class="flex items-center gap-3 mt-2 px-1">
        <div class="block w-40">Mã giảm giá</div>
        <div
          class="block w-full items-center outline-none px-2 py-1 rounded relative bg-secondary"
        >
          <input
            type="text"
            class="outline-none bg-secondary"
            v-model="voucherCode"
            @input="convertToUpperCase"
          />
          <div
            @click="handleAddVoucher"
            class="absolute right-0 top-0 bg-primary text-white px-1 md:px-2 py-1 z-1 rounded-r cursor-pointer"
          >
            Áp dụng
          </div>
        </div>
      </div>
      <div
        @click="handleOpenVoucherPopup"
        class="flex items-center mt-1 justify-end text-xs text-primary cursor-pointer"
      >
        Xem thêm mã giảm giá
      </div>
    </div>
    <!-- <div class="px-1 flex items-center gap-1">
      <div>Chiết khấu:</div>
      <div>Đại lý cấp 1</div>
    </div> -->
    <!--  -->
    <div class="bg-secondary p-1 rounded">
      <div class="flex items-center justify-between py-1">
        <div class="text-primary">Tổng số tiền</div>
        <div class="text-primary">
          {{ formatCurrency(provisional + orderStore.shippingFee || 0) }}
        </div>
      </div>
      <div class="flex items-center justify-between py-1">
        <div class="text-primary">Giảm giá</div>
        <div class="text-primary">
          {{
            formatCurrency(
              provisional - orderDetail?.order?.discountTotalPrice?.amount || 0
            )
          }}
        </div>
      </div>
      <div class="flex items-center justify-between py-1">
        <div class="text-primary">Sau giảm</div>
        <div class="text-primary">
          {{
            formatCurrency(
              orderDetail?.order?.discountTotalPrice?.amount +
                orderStore.shippingFee || 0
            )
          }}
        </div>
      </div>
      <div
        v-if="
          orderStore.shippingFee > 0 ||
          orderDetail?.order?.totalShippingPrice?.amount > 0
        "
        class="flex items-center justify-between"
      >
        <div class="text-primary">Phí vận chuyển</div>
        <div class="text-primary">
          {{
            formatCurrency(
              orderStore.shippingFee ||
                orderDetail?.order?.totalShippingPrice?.amount
            )
          }}
        </div>
      </div>
      <div class="flex items-center justify-between py-1">
        <div class="text-primary">Đã thanh toán</div>
        <div class="text-primary">
          {{
            formatCurrency(
              orderDetail?.order?.currentTotalPrice?.amount - totalPrice || 0
            )
          }}
        </div>
      </div>
      <!--  -->
      <div class="border-b mx-2 pt-1"></div>
      <div class="flex items-center justify-between py-1">
        <div class="text-primary">Cần thanh toán</div>
        <div class="font-bold text-red-500">
          {{
            formatCurrency(
              orderStore.shippingFee + totalPrice || orderStore.shippingFee + 0
            )
          }}
        </div>
      </div>
    </div>
    <VoucherMessage
      v-if="isOpenVoucherPopup"
      @confirm="handleOpenVoucherPopup"
      @cancel="handleOpenVoucherPopup"
    ></VoucherMessage>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const totalPrice = computed(() => orderStore.orderDetail?.remainTotal);
const provisional = computed(
  () => orderStore.orderDetail?.order?.currentSubtotalPrice?.amount
);
const vouchers = computed(() =>
  orderStore.orderDetail?.order?.discountApplications?.filter(
    (item: any) => item.type === "VOUCHER"
  )
);
const memberLevel = computed(() =>
  orderStore.orderDetail?.order?.discountApplications?.find(
    (item: any) => item.type === "MEMBER"
  )
);
const voucherCode = ref();
const { addVoucher, removeVoucher, removeMemberDiscount } = useOrder();
const { checkValidVoucher } = useCampaign();
const customer = computed(() => orderStore.customerInOrder);
const orderDetail = computed(() => orderStore.orderDetail);
const isOpenVoucherPopup = ref(false);
const handleAddVoucher = async () => {
  if (voucherCode.value) {
    const response = await checkValidVoucher(
      customer.value?.id,
      voucherCode.value
    );
    if (response) {
      await addVoucher(orderDetail.value.id, voucherCode.value);
      voucherCode.value = "";
      await orderStore.getOrderById(orderDetail.value.id);
    }
  }
};
const convertToUpperCase = () => {
  voucherCode.value = voucherCode.value.toUpperCase();
};
const handleRemoveVoucher = async (item: any) => {
  await removeVoucher(orderDetail.value.id, item.voucherCode);
  await orderStore.getOrderById(orderDetail.value.id);
};
const discountType = ref("MONEY");
const discountValue = ref();
const {
  updateDiscount,
  getOrderPromotion,
  updateMemberDiscount,
  updateInfoCampaignPromotion,
} = useOrder();

watch(
  () => discountType.value,
  async (newVal, oldVal) => {
    discountType.value = newVal;
    discountValue.value = 0;
    const data = {
      type: discountType.value,
      amount: +discountValue.value,
    };
    await updateDiscount(orderDetail.value?.id, "", data);
    await orderStore.getOrderById(orderDetail.value?.id);

    // await updateDiscountPriceInOrder(
    //   props.product?.order_id,
    //   props.product?.id,
    //   data
    // );
  }
);
watch(
  () => discountValue.value,
  async (newVal, oldVal) => {
    discountValue.value = newVal;
    const data = {
      type: discountType.value,
      amount: +discountValue.value,
    };
    await updateDiscount(orderDetail.value?.id, "", data);
    await orderStore.getOrderById(orderDetail.value?.id);
    // await updateDiscountPriceInOrder(
    //   props.product?.order_id,
    //   props.product?.id,
    //   data
    // );
  }
);

const handleOpenVoucherPopup = () => {
  isOpenVoucherPopup.value = !isOpenVoucherPopup.value;
};
//
const campaignLevel = computed(() => orderStore.campaignLevel);
const isMemberLevel = ref(false);

watch(
  () => orderDetail.value?.order?.discountApplications,
  (newVal) => {
    const response = newVal?.find((item: any) => item.type === "MEMBER");
    if (response) {
      isMemberLevel.value = true;
    } else {
      isMemberLevel.value = false;
    }
  },
  { immediate: true }
);
const handleToogleMemberLevel = async (event: any) => {
  event.stopPropagation();
  if (customer.value?.memberLevel) {
    // Lưu trạng thái checkbox ban đầu
    const originalState = isMemberLevel.value;
    const newMemberLevelState = !originalState;

    try {
      if (newMemberLevelState) {
        // Áp dụng giảm giá hạng thành viên
        if (customer.value?.memberLevel) {
          const response = await getOrderPromotion(customer.value?.memberLevel);

          // Kiểm tra response.status từ getOrderPromotion
          if (response?.status !== 1 || !response?.data) {
            // Khôi phục trạng thái ban đầu nếu getOrderPromotion thất bại
            isMemberLevel.value = originalState;
            return;
          }

          const data = {
            type: response.data.discountType,
            amount: response.data.discount,
            campaignId: response.data.campaignId,
            campaignActionId: response.data.campaignId,
            campaignActionType: "PROMOTION_ORDER_MEMBER",
          };

          const updateResponse = await updateMemberDiscount(
            orderDetail.value?.id,
            data
          );

          // Kiểm tra updateMemberDiscount thành công
          if (updateResponse?.status !== 1) {
            // Khôi phục trạng thái ban đầu nếu updateMemberDiscount thất bại
            isMemberLevel.value = originalState;
            return;
          }

          // Cập nhật order
          await orderStore.getOrderById(orderDetail.value.id);

          // Chỉ cập nhật checkbox khi TẤT CẢ các bước đều thành công
          isMemberLevel.value = newMemberLevelState;
        }
      } else {
        // Bỏ giảm giá hạng thành viên
        const removeResponse = await removeMemberDiscount(
          orderDetail.value?.id
        );

        // Kiểm tra removeMemberDiscount thành công
        if (removeResponse?.status !== 1) {
          // Khôi phục trạng thái ban đầu nếu removeMemberDiscount thất bại
          isMemberLevel.value = originalState;
          return;
        }

        // Cập nhật order
        await orderStore.getOrderById(orderDetail.value.id);

        // Chỉ cập nhật checkbox khi TẤT CẢ các bước đều thành công
        isMemberLevel.value = newMemberLevelState;
      }
    } catch (error) {
      console.error("Error handling member level toggle:", error);
      // Khôi phục trạng thái checkbox về ban đầu khi có bất kỳ exception nào
      isMemberLevel.value = originalState;
    }
  }
};
// watch(
//   () => isMemberLevel.value,
//   async (newVal, oldVal) => {
//     if (newVal) {
//       const campaignAction = campaignLevel.value?.campaignActions.find(
//         (action: any) => action.type === "PROMOTION_ORDER_MEMBER"
//       );
//     } else {
//     }
//   }
// );

// Import utilities
import { getPaymentStatusClass } from "~/utils/statusHelpers";
</script>
