<template>
  <div class="m-2 h-screen-50 overflow-y-auto">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-2">
      <div class="col-span-12 md:col-span-4">
        <NavigationTimeKeeping
          @createWorkEffort="handleCreateWorkEffort"
          @selectWorkEffort="handleSelectWorkEffort"
          @employeeSelected="handleSelectedEmployee"
          @dateRangeChanged="handleChangeDate"
          @completeWorkEffort="completeWorkEffort"
          :dataWorkEffort="dataWorkEffort"
          :selectedWorkEffortId="selectedWorkEffortId"
          :dataEmployee="dataEmployee"
        ></NavigationTimeKeeping>
      </div>

      <div class="col-span-12 md:col-span-8 md:block hidden">
        <TabTimeKeeping
          :selectedWorkEffortId="selectedWorkEffortId"
        ></TabTimeKeeping>
      </div>
    </div>
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chấm công",
});

const auth = useCookie("auth") as any;
const dataWorkEffort = ref<any>([]);
const selectedWorkEffortId = ref<any>();
const { getListWorkEfforts, createWorkEfforts } = useTimeKeeping();
const { fetchDataEmployees } = useOrder();
const { updateWorkEffortStatus } = useCrm();
const { storeId } = useTabContext();
const isLoading = ref(false);
const handleGetWorkEffort = async (
  employeeId: string,
  fromDate: any,
  toDate: any
) => {
  const attributes = {
    storeId: storeId.value,
    createdBy: employeeId,
  };

  try {
    const response = await getListWorkEfforts(
      employeeId,
      "",
      "TIMEKEEPING",
      0,
      10,
      attributes,
      fromDate,
      toDate
    );

    dataWorkEffort.value = response?.data;
    if (response?.data?.length > 0) {
      selectedWorkEffortId.value = response?.data[0];
    }
  } catch (error) {
    console.error("Error fetching work effort:", error);
  }
};
//

const handleCreateWorkEffort = async () => {
  isLoading.value = true;
  const attributes = {
    storeId: storeId.value,
  };
  try {
    const response = await createWorkEfforts(
      auth.value?.user?.id,
      dataWorkEffort.value[0]?.workflowId === "CHECK_IN"
        ? `${auth.value?.user?.name} ra ca  `
        : `${auth.value?.user?.name} vào ca`,
      "",
      dataWorkEffort.value[0]?.workflowId === "CHECK_IN"
        ? "CHECK_OUT"
        : "CHECK_IN",
      "TIMEKEEPING",
      attributes,
      ""
    );
    await handleGetWorkEffort(auth.value?.user?.id, "", "");
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

const handleSelectWorkEffort = (workEffort: string) => {
  selectedWorkEffortId.value = workEffort;
};
const dataEmployee = ref<any>([]);
const hanldeGetDataEmployee = async () => {
  try {
    const response = await fetchDataEmployees();
    dataEmployee.value = response;
  } catch (error) {
    throw error;
  }
};
const handleSelectedEmployee = async (employee: any) => {
  await handleGetWorkEffort(employee?.id, "", "");
};
const handleChangeDate = async (dateRange: any) => {
  await handleGetWorkEffort(
    auth.value?.user?.id,
    dateRange?.dateFrom,
    dateRange?.dateTo
  );
};
const completeWorkEffort = async (task: any) => {
  console.log("task", task?.id);
  console.log("data", dataWorkEffort.value);
  try {
    await updateWorkEffortStatus(
      auth.value?.user?.id,
      task.id,
      "TIMEKEEPING",
      "DONE"
    );
    await handleGetWorkEffort(auth.value?.user?.id, "", "");
    useNuxtApp().$toast.success("Chúc mùng bạn đã hoàn thành công việc");
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await Promise.allSettled([
    handleGetWorkEffort(auth.value?.user?.id, "", ""),
    hanldeGetDataEmployee(),
  ]);
});
</script>
