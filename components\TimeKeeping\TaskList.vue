<template>
  <div>
    <!-- Task List Header -->
    <div class="flex items-center justify-between">
      <label class="font-semibold text-sm"><PERSON>h sách công việc</label>
      <div
        @click="handleCreateWorkEffort"
        class="bg-primary text-white px-2 py-1 rounded flex items-center cursor-pointer"
      >
        {{
          dataWorkEffort[0]?.workflowId === "CHECK_IN"
            ? "Kết thúc ca"
            : "Bắt đầu ca"
        }}
      </div>
    </div>

    <!-- Task List -->
    <div class="space-y-3 mt-3 md:h-[60vh] h-[65vh] overflow-y-auto">
      <div
        v-for="task in dataWorkEffort"
        :key="task.id"
        @click="handleSelectWorkEffort(task)"
        class="rounded-lg p-3 transition-all duration-200 cursor-pointer bg-white border border-gray-200 hover:shadow-sm hover:border-gray-300"
      >
        <!-- Task Header -->
        <div class="flex items-start justify-between mb-2">
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium truncate text-gray-900">
              {{ task?.name }}
            </h4>
            <p class="text-xs mt-1 text-gray-500"></p>
          </div>

          <!-- Selected indicator -->
          <div
            v-if="selectedWorkEffortId?.id === task.id && isMobile"
            class="flex items-center justify-center w-6 h-6 bg-primary rounded-full"
          >
            <svg
              class="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>

        <!-- Task Details -->
        <div class="space-y-2">
          <!-- Assignee -->
          <div class="flex items-center gap-2">
            <svg
              class="w-3 h-3 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            <span class="text-xs cursor-help text-gray-600">
              {{ getEmployeeName(task?.createdBy) }}
            </span>
          </div>

          <!-- Time -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <svg
                class="w-3 h-3 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span class="text-xs text-gray-600">
                {{ formatTimestampV3(task?.createdStamp) }}
              </span>
            </div>
            <button
              v-if="task?.status !== 4"
              @click.stop="handleCompleteWorkEffort(task)"
              class="px-3 py-1 text-xs bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors duration-200"
            >
              Hoàn thành
            </button>
            <span
              v-else
              class="px-3 py-1 text-xs bg-gray-100 text-gray-500 rounded-md"
            >
              Đã hoàn thành
            </span>
          </div>
        </div>
        <!-- task editor -->
        <TiptapEditor
          v-if="!isMobile"
          @update:modelValue="handleUpdateContent(task?.id)"
          :modelValue="getTaskContent(task?.id)"
          :placeholder="'Nhập nội dung công việc...'"
          :showCharacterCount="true"
          :editable="!isLoading"
          class="transition-opacity duration-200 h-[250px]"
          :class="{ 'opacity-50 pointer-events-none': isLoading }"
        ></TiptapEditor>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const emit = defineEmits([
  "createWorkEffort",
  "selectWorkEffort",
  "dataEmployee",
  "completeWorkEffort",
]);

const props = defineProps<{
  dataWorkEffort: any[];
  selectedWorkEffortId: any | null;
  isMobile: Boolean;
  dataEmployee?: any[];
}>();

// Computed properties
const getEmployeeName = (createdBy: string) => {
  if (!createdBy) return "Không xác định";

  if (!props.dataEmployee || !Array.isArray(props.dataEmployee)) {
    return createdBy; // Trả về createdBy gốc nếu không có dataEmployee
  }
  const employee = props.dataEmployee.find((emp: any) => {
    return emp.id === createdBy;
  });
  if (employee) {
    const employeeName = employee.name;
    if (employeeName !== createdBy && employeeName !== employee.id) {
      return employeeName;
    }
  }
  return createdBy;
};

// Functions
const handleCreateWorkEffort = () => {
  emit("createWorkEffort");
};

const handleSelectWorkEffort = (workEffort: any) => {
  if (!props.isMobile) return;
  emit("selectWorkEffort", workEffort);
};
// Methods
const { getContent, handleSaveTextEditor } = usePortal();
const isLoading = ref(false);
const taskContents = ref<Record<string, string>>({});

// Hàm cập nhật content cho task cụ thể
const handleUpdateContent = (taskId: string) => {
  // return async (content: string) => {
  //   if (!taskId) return;
  //   const data = {
  //     type: "DESCRIPTION_WORKEFFORT",
  //     content: content,
  //     relativeId: taskId,
  //     version: new Date(),
  //     createdBy: "hung",
  //   };
  //   try {
  //     await handleSaveTextEditor(data);
  //     // Cập nhật local cache
  //     taskContents.value[taskId] = content;
  //   } catch (error) {
  //     throw error;
  //   }
  // };
};

// Hàm lấy content cho task cụ thể
const getTaskContent = (taskId: string) => {
  if (!taskId) return "";

  // Nếu đã có trong cache, trả về ngay
  if (taskContents.value[taskId] !== undefined) {
    return taskContents.value[taskId];
  }

  // Nếu chưa có, load từ server
  loadTaskContent(taskId);
  return "";
};

// Hàm load content từ server
const loadTaskContent = async (taskId: string) => {
  if (!taskId || isLoading.value) return;

  try {
    isLoading.value = true;
    const response = await getContent("DESCRIPTION_WORKEFFORT", taskId);
    taskContents.value[taskId] = response?.content || "";
  } catch (error) {
    console.error("Error loading task content:", error);
    taskContents.value[taskId] = "";
  } finally {
    isLoading.value = false;
  }
};
const handleCompleteWorkEffort = async (task: any) => {
  emit("completeWorkEffort", task);
};
</script>

<style scoped>
/* Custom scrollbar */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}
</style>
