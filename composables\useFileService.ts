export default function useFileService() {
  const $sdk = useNuxtApp().$sdk;
  const uploadImage = async (
    imageFile: File,
    path?: string,
    parentId?: string,
    parentType?: string
  ) => {
    try {
      const response = await $sdk.fileService.uploadImage(
        imageFile,
        path,
        parentId,
        parentType
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getImage = async (parentId: string, parenttype: string) => {
    try {
      const response = await $sdk.fileService.getImage(parentId, parenttype);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return { uploadImage, getImage };
}
