<template>
  <div :class="containerClass">
    <!-- Empty State -->
    <div v-if="!dataCheckin?.length" class="text-center py-12">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
        />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Ch<PERSON><PERSON> có dữ liệu chấm công
      </h3>
      <p class="text-gray-500">Nhân viên chưa thực hiện chấm công nào</p>
    </div>

    <!-- Time Keeping Items -->
    <div
      v-for="(item, index) in dataCheckin"
      :key="index"
      :class="itemWrapperClass"
    >
      <div
        @click="handleItemClick(item)"
        :class="[cardClass, 'cursor-pointer']"
      >
        <!-- Detailed View (Employee) -->
        <div
          v-if="variant === 'detailed'"
          class="flex items-center justify-between"
        >
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-primary"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div>
              <div class="font-semibold text-gray-900">
                {{ item?.owner?.name || "Nhân viên" }}
              </div>
              <div class="text-sm text-gray-500">
                {{ formatDate(item.createdStamp) }} •
                {{ formatTime(item.createdStamp) }}
              </div>
            </div>
          </div>

          <div :class="getActionClass(item.workEffortTypeId)">
            {{ formatAction(item.workEffortTypeId) }}
          </div>
        </div>

        <!-- Compact View (Manager) -->
        <div v-else>
          <div class="font-bold">{{ item?.owner?.name }}</div>
          <div class="flex items-center justify-between">
            <div class="">{{ formatDate(item.createdStamp) }}</div>
            <div :class="getCompactActionClass(item.workEffortTypeId)">
              {{ formatAction(item.workEffortTypeId) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals -->
  <ModalEditCheckin v-if="isEditCheckin" @isClose="isClose" />
  <ModalTimeKeepingDetail
    :isOpen="showDetailModal"
    :data="selectedItem"
    @close="closeDetailModal"
    @viewDetail="handleViewFullDetail"
  />
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";

// Props
interface Props {
  dataCheckin: any[];
  variant?: "detailed" | "compact";
}

const props = withDefaults(defineProps<Props>(), {
  variant: "detailed",
});

const isEditCheckin = ref(false);
const showDetailModal = ref(false);
const selectedItem = ref<any>(null);
const timeKeepingStore = useTimeKeepingStore();

const isClose = (value: boolean) => {
  isEditCheckin.value = value;
};

// Modal functions
const handleItemClick = async (item: any) => {
  // Fetch full details if needed
  try {
    const { getWorkEffortById } = useTimeKeeping();
    const fullData = await getWorkEffortById(item.id);
    selectedItem.value = fullData;
    showDetailModal.value = true;
    timeKeepingStore.setTimeKeeping(item);
  } catch (error) {
    console.error("Error fetching item details:", error);
    // Fallback to basic item data
    selectedItem.value = item;
    showDetailModal.value = true;
    timeKeepingStore.setTimeKeeping(item);
  }
};

const closeDetailModal = () => {
  showDetailModal.value = false;
  selectedItem.value = null;
};

const handleViewFullDetail = (id: string) => {
  const storeId = useCookie("storeId").value;
  navigateTo(`/timekeeping/${id}?storeId=${storeId}`);
};

// Computed classes based on variant
const containerClass = computed(() => {
  return props.variant === "detailed" ? "space-y-3" : "w-full";
});

const itemWrapperClass = computed(() => {
  return props.variant === "detailed" ? "" : "mt-4 border-b";
});

const cardClass = computed(() => {
  if (props.variant === "detailed") {
    return "bg-white rounded-lg p-4 border border-gray-200 hover:border-primary hover:shadow-md transition-all duration-200";
  } else {
    return "bg-white rounded-lg h-auto z-1 p-2 border-primary";
  }
});

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "dd/MM/yyyy", { locale: vi });
};

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm", { locale: vi });
};

const formatAction = (value: string) => {
  if (value === "CHECK_IN") {
    return "Vào ca";
  }
  if (value === "CHECK_OUT") {
    return "Hết ca";
  }
  return value;
};

// Action class for detailed view (original CardTimeKeeping style)
const getActionClass = (value: string) => {
  const baseClass =
    "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
  if (value === "CHECK_IN") {
    return `${baseClass} bg-green-100 text-green-800`;
  }
  if (value === "CHECK_OUT") {
    return `${baseClass} bg-red-100 text-red-800`;
  }
  return `${baseClass} bg-gray-100 text-gray-800`;
};

// Action class for compact view (original CardTimeKeepingManager style)
const getCompactActionClass = (value: string) => {
  switch (value) {
    case "CHECK_IN":
      return "text-green-500 text-sm bg-green-100 p-2 px-2 rounded-xl";
    case "CHECK_OUT":
      return "text-red-500 text-sm bg-red-50 p-2 px-2 rounded-xl";
    default:
      return "";
  }
};
</script>
