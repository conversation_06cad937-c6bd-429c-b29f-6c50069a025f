<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    @click="closeModal"
  >
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black/50 transition-opacity"></div>

    <!-- Modal -->
    <div
      class="relative bg-secondary-light rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
      @click.stop
    >
      <!-- Header -->
      <div
        class="sticky top-0 bg-primary text-white border-b border-gray-200 px-6 py-4 rounded-t-xl"
      >
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">Chi tiết chấm công</h2>
          <button
            @click="closeModal"
            class="flex items-center justify-center w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-200"
          >
            <svg
              class="w-5 h-5 text-gray-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <div class="p-2 space-y-2">
        <div class="bg-white rounded-lg p-2">
          <h3
            class="text-sm font-semibold text-gray-900 mb-4 flex items-center gap-2"
          >
            <svg
              class="w-5 h-5 text-primary"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
            Thông tin nhân viên
          </h3>

          <div class="flex items-center gap-2">
            <div class="relative">
              <img
                class="w-16 h-16 rounded-full border-2 border-gray-200 object-cover"
                :src="data?.owner?.avatar || 'https://placehold.co/80'"
                loading="lazy"
                :alt="data?.owner?.name"
              />
              <div
                class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"
              ></div>
            </div>

            <div class="flex-1">
              <h4 class="font-semibold text-sm">
                {{ data?.owner?.name || "Không có tên" }}
              </h4>
              <div class="flex items-center gap-2 text-gray-600 mt-1 text-sm">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                  />
                </svg>
                <span>{{ data?.owner?.phone || "Chưa có số điện thoại" }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg p-2">
          <h3 class="text-sm font-semibold mb-4 flex items-center gap-2">
            <svg
              class="w-5 h-5 text-primary"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clip-rule="evenodd"
              />
            </svg>
            Thông tin thời gian
          </h3>

          <div class="grid grid-cols-2 gap-2">
            <div class="bg-secondary-light rounded-lg p-2">
              <div class="text-sm text-gray-600 mb-1">Ngày</div>
              <div class="text-sm font-semibold text-gray-900">
                {{ formatDateOnly(data?.createdStamp) }}
              </div>
            </div>

            <div class="bg-secondary-light rounded-lg p-2">
              <div class="text-sm text-gray-600 mb-1">Thời gian</div>
              <div class="text-sm font-semibold text-gray-900">
                {{ formatTimeOnly(data?.createdStamp) }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="hasImages" class="bg-white rounded-lg p-2">
          <h3
            class="text-sm font-semibold text-gray-900 mb-2 flex items-center gap-2"
          >
            <svg
              class="w-5 h-5 text-primary"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clip-rule="evenodd"
              />
            </svg>
            Hình ảnh chấm công
            <span class="text-sm font-normal text-gray-500"
              >({{ imageUrls.length }} ảnh)</span
            >
          </h3>

          <div class="grid grid-cols-2 gap-2">
            <div
              v-for="(url, index) in imageUrls"
              :key="index"
              class="relative bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
              @click="openImageModal(url)"
            >
              <img
                :src="url"
                :alt="`Ảnh chấm công ${index + 1}`"
                class="w-full h-32 object-cover"
                loading="lazy"
              />
            </div>
          </div>
        </div>

        <div v-else class="bg-gray-50 rounded-lg p-8 text-center">
          <svg
            class="w-12 h-12 text-gray-400 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <h4 class="text-lg font-medium text-gray-900 mb-2">
            Không có hình ảnh
          </h4>
          <p class="text-gray-500">Chấm công này không có hình ảnh đính kèm</p>
        </div>
      </div>
    </div>

    <div
      v-if="selectedImage"
      class="fixed inset-0 bg-black/80 flex items-center justify-center z-60 p-4"
      @click="closeImageModal"
    >
      <div class="relative max-w-4xl max-h-full">
        <img
          :src="selectedImage"
          class="max-w-full max-h-full object-contain"
        />
        <button
          @click="closeImageModal"
          class="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";
import {
  getTimekeepingTypeText,
  getTimekeepingStatusClass,
} from "~/utils/statusHelpers";

interface Props {
  isOpen: boolean;
  data: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
  viewDetail: [id: string];
}>();

const selectedImage = ref<string | null>(null);
const storeId = useCookie("storeId").value;

// Computed properties
const imageUrls = computed(() => {
  if (
    !props.data?.attachments ||
    !Array.isArray(props.data.attachments) ||
    props.data.attachments.length === 0
  ) {
    return [];
  }

  const { getUrlImageTimeKeeping } = useTimeKeeping();
  const endpoint = getUrlImageTimeKeeping();

  return props.data.attachments.map((item: any) => {
    return `${endpoint}/${item.srcPath}`;
  });
});

const hasImages = computed(() => imageUrls.value.length > 0);

// Format functions
const formatDateOnly = (timestamp: number) => {
  if (!timestamp) return "Không có dữ liệu";
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "eeee, dd/MM/yyyy", { locale: vi });
};

const formatTimeOnly = (timestamp: number) => {
  if (!timestamp) return "Không có dữ liệu";
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm:ss", { locale: vi });
};

const getTypeColor = (type: string) => {
  if (type === "CHECK_IN") return "text-green-700";
  if (type === "CHECK_OUT") return "text-red-700";
  return "text-gray-700";
};

// Modal functions
const closeModal = () => {
  emit("close");
};

const viewFullDetail = () => {
  emit("viewDetail", props.data.id);
  closeModal();
};

const openImageModal = (url: string) => {
  selectedImage.value = url;
};

const closeImageModal = () => {
  selectedImage.value = null;
};
</script>
