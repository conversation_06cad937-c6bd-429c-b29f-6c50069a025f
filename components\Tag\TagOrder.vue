<template>
  <div class="bg-white transition-all duration-200">
    <!-- Header Section -->
    <div class="flex items-center justify-between p-3 border-b border-gray-100">
      <div class="flex items-center gap-1">
        <div class="flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            class="size-4 text-primary"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
            />
          </svg>
        </div>
        <h3 class="text-sm font-semibold text-primary">Nhật ký</h3>
        <span
          v-if="dataConnector?.length > 0"
          class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"
        >
          {{ dataConnector.length }}
        </span>
      </div>
      <button
        @click="handleOpenTag"
        class="flex items-center justify-center transition-all duration-200"
        :class="{ 'bg-gray-100': isOpen }"
        :aria-expanded="isOpen"
        aria-label="Toggle tag section"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          class="w-4 h-4 text-gray-600 transition-transform duration-200"
          :class="{ 'rotate-180': isOpen }"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m19.5 8.25-7.5 7.5-7.5-7.5"
          />
        </svg>
      </button>
    </div>

    <!-- Content Section -->
    <Transition
      name="slide-fade"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform -translate-y-2"
    >
      <div v-if="isOpen" class="p-3 pt-0">
        <!-- Tag Areas -->
        <div class="space-y-3 mb-3" v-if="dataConnector?.length > 0">
          <TagArea
            v-for="tag in dataConnector"
            :key="tag._id"
            :tag="tag"
            class="transition-all duration-200"
          />
        </div>

        <!-- Empty State -->
        <div
          v-else
          class="flex flex-col items-center justify-center py-6 text-center"
        >
          <div
            class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-6 h-6 text-gray-400"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"
              />
            </svg>
          </div>
          <p class="text-sm text-gray-500 mb-1">Chưa có nhật ký nào</p>
          <p class="text-xs text-gray-400">Thêm nhật ký đầu tiên để bắt đầu</p>
        </div>

        <!-- Add Button -->
        <button
          @click="handleCreateConnection"
          :disabled="isCreating"
          class="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-primary bg-primary/5 border border-primary/20 rounded-lg transition-all duration-200 hover:bg-primary/10 hover:border-primary/30 focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg
            v-if="!isCreating"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
          <div
            v-else
            class="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"
          ></div>
          <span>{{ isCreating ? "Đang thêm..." : "Thêm nhật ký" }}</span>
        </button>
      </div>
    </Transition>
  </div>
</template>
<script setup lang="ts">
// Lazy load heavy components with better error handling
const TagArea = defineAsyncComponent({
  loader: () => import("~/components/Tag/TagArea.vue"),
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-100 h-20 rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 text-sm p-2" }, "Lỗi tải component"),
  delay: 200,
  timeout: 3000,
});

// Composables
const { createConnector } = usePortal();
const route = useRoute();
const tagStore = useTagStore();

// Computed properties
const dataConnector = computed(() => tagStore.dataConnector);

// Reactive state
const isOpen = ref(false);
const isCreating = ref(false);

// Methods
const handleCreateConnection = async () => {
  if (isCreating.value || !route.query.orderId) return;

  try {
    isCreating.value = true;
    await createConnector(
      route.query.orderId as string,
      "ORDER",
      "",
      "TAG",
      ""
    );
    await tagStore.handleGetConnectorByResource(
      route.query.orderId as string,
      "ORDER",
      "TAG"
    );
  } catch (error) {
    console.error("Error creating connection:", error);
    // You can add toast notification here
  } finally {
    isCreating.value = false;
  }
};

const handleOpenTag = () => {
  isOpen.value = !isOpen.value;
};

// Route-based state management
const updateOpenStateBasedOnRoute = (path: string) => {
  if (path === "/diary") {
    isOpen.value = true;
  } else if (path === "/sale") {
    isOpen.value = false;
  }
};

// Watch for route changes
watch(
  () => route.path,
  (newPath) => {
    updateOpenStateBasedOnRoute(newPath);
  },
  { immediate: true }
);

// Initialize state on mount
onMounted(() => {
  updateOpenStateBasedOnRoute(route.path);
});

// Cleanup
onUnmounted(() => {
  // Any cleanup if needed
});
</script>
