
export default function useTimeKeeping() {
  const $sdk = useNuxtApp().$sdk;
  const token = useCookie("token").value;
  if (token) {
    $sdk.setToken(token);
  } else {
    throw new Error("Token is not defined");
  }
  const timeKeepingStore = useTimeKeepingStore();
  const createWorkEfforts = async (
    createdBy: string,
    name: string,
    decription: string,
    workEffortTypeId: string,
    source: string,
    attributes: object,
    parentId: string
  ) => {
    try {
      const response = await $sdk.crm.createWorkEffort(
        createdBy,
        name,
        decription,
        workEffortTypeId,
        source,
        attributes,
        parentId
      );
      // await getListWorkEfforts(createdBy, "", source, 0, 10, attributes);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getListWorkEffortTypes = async (id: any) => {
    try {
      const response = await $sdk.crm.getListWorkEffortType(id);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getListWorkEfforts = async (
    performerId: string,
    workEffortTypeId: string,
    source: string,
    pageNumber: number,
    pageSize: number,
    attributes: object,
    fromDate: any,
    toDate: any
  ) => {
    const sorts = {
      key: "createdStamp",
      asc: false,
    };
    try {
      const response = await $sdk.crm.getWorkEfforts(
        performerId,
        workEffortTypeId,
        source,
        pageNumber,
        pageSize,
        sorts,
        attributes,
        fromDate,
        toDate
      );
      timeKeepingStore.setDataTimeKeeping(response.data);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getListWorkEffortsV1 = async (data: any) => {
    const sorts = {
      key: "createdStamp",
      asc: false,
    };
    try {
      const response = await $sdk.crm.getWorkEfforts(
        data.performerId,
        data.workEffortTypeId,
        data.source,
        data.pageNumber,
        data.pageSize,
        sorts,
        data.attributes,
        data?.fromDate,
        data?.toDate
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getWorkEffortById = async (id: string) => {
    try {
      const response = await $sdk.crm.getWorkEffortById(id);
      return response;
    } catch (error) {
      throw error;
    }
  };
  //
  const getMyWorkEffortToday = async (performerId: string) => {
    try {
      const response = await $sdk.crm.getMyWorkEffortToday(
        performerId,
        "SHIFT"
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getServiceActions = async (serviceId: string) => {
    try {
      const response = await $sdk.service.getServiceActions(serviceId);
      console.log("response", response);
    } catch (error) {
      throw error;
    }
  };
  const getUrlImageTimeKeeping = () => {
    return $sdk.getImage.endpoint;
  };
  return {
    createWorkEfforts,
    getListWorkEfforts,
    getListWorkEffortTypes,
    getWorkEffortById,
    getListWorkEffortsV1,
    getMyWorkEffortToday,
    getServiceActions,
    getUrlImageTimeKeeping,
  };
}
