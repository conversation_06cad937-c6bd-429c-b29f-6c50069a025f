<template>
  <div
    class="bg-white border border-gray-200 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
  >
    <!-- Description Section -->
    <div class="p-3 border-b border-gray-100">
      <div class="relative">
        <textarea
          v-model="tagDescription"
          :disabled="isNotDraft"
          rows="2"
          class="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg resize-none outline-none transition-all duration-200 disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed"
          :class="{
            'bg-gray-50 border-gray-100': isNotDraft,
            'bg-white hover:border-gray-300': !isNotDraft,
          }"
          placeholder="Mô tả tình huống..."
          @blur="updateTagDescription"
          @input="handleDescriptionInput"
        />

        <!-- Character count -->
        <div
          v-if="!isNotDraft && tagDescription"
          class="absolute bottom-2 right-2 text-xs text-gray-400"
        >
          {{ tagDescription.length }}/500
        </div>
      </div>
    </div>

    <!-- Tags Section -->
    <div class="p-3" @click.stop="focusInput">
      <div class="relative">
        <!-- Tags Container -->
        <div class="flex flex-wrap gap-2 min-h-[2.5rem] items-start">
          <!-- Existing Tags -->
          <TransitionGroup
            name="tag"
            tag="div"
            class="contents"
            enter-active-class="transition-all duration-200 ease-out"
            enter-from-class="opacity-0 scale-95 transform"
            enter-to-class="opacity-100 scale-100 transform"
            leave-active-class="transition-all duration-150 ease-in"
            leave-from-class="opacity-100 scale-100 transform"
            leave-to-class="opacity-0 scale-95 transform"
          >
            <div
              v-for="(tag, index) in dataTag"
              :key="`tag-${tag.id || index}`"
              class="group relative inline-flex items-center gap-1 px-3 py-1.5 bg-primary/10 text-primary text-sm font-medium rounded-full border border-primary/20 transition-all duration-200 hover:bg-primary/15 hover:border-primary/30"
            >
              <!-- Tag Icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                class="w-3 h-3"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"
                />
              </svg>

              <!-- Tag Title -->
              <span class="truncate max-w-[120px]">{{ tag?.title }}</span>

              <!-- Remove Button -->
              <button
                v-if="!isNotDraft"
                @click.stop="handleRemoveTag(tag)"
                class="ml-1 p-0.5 rounded-full transition-all duration-200 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-200"
                :aria-label="`Xóa tag ${tag?.title}`"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="w-3 h-3 text-red-500 hover:text-red-600"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </TransitionGroup>

          <!-- Input Field -->
          <div v-if="!isNotDraft" class="relative flex-1 min-w-[120px]">
            <input
              ref="inputRef"
              v-model="tagTitle"
              :key="inputKey"
              :id="tag?._id"
              type="text"
              class="w-full px-3 py-1.5 text-sm bg-gray-50 border border-gray-200 rounded-full transition-all duration-200 outline-none"
              placeholder="Nhập tag mới..."
              @keyup.enter="handleAddOrCreateTag"
              @keyup.escape="clearInput"
              @input="handleInputChange"
              autocomplete="off"
            />

            <!-- Search Dropdown -->
            <Transition
              enter-active-class="transition-all duration-200 ease-out"
              enter-from-class="opacity-0 transform scale-95 -translate-y-2"
              enter-to-class="opacity-100 transform scale-100 translate-y-0"
              leave-active-class="transition-all duration-150 ease-in"
              leave-from-class="opacity-100 transform scale-100 translate-y-0"
              leave-to-class="opacity-0 transform scale-95 -translate-y-2"
            >
              <div
                v-if="dataSearchTag?.length > 0 && tagTitle.trim()"
                class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto"
              >
                <div class="p-1">
                  <div
                    v-for="search in dataSearchTag"
                    :key="search.id"
                    @click.prevent="handleAddTag(search)"
                    class="flex items-center justify-between px-3 py-2 text-sm rounded-md cursor-pointer transition-all duration-150 hover:bg-gray-50 focus:bg-gray-50"
                    :class="{
                      'text-primary font-medium': search.id === 'addTAG',
                      'text-gray-700': search.id !== 'addTAG',
                    }"
                  >
                    <div class="flex items-center gap-2">
                      <svg
                        v-if="search.id === 'addTAG'"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="2"
                        stroke="currentColor"
                        class="w-4 h-4 text-primary"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M12 4.5v15m7.5-7.5h-15"
                        />
                      </svg>
                      <svg
                        v-else
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="2"
                        stroke="currentColor"
                        class="w-4 h-4 text-gray-400"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"
                        />
                      </svg>
                      <span class="truncate">
                        {{
                          search.id === "addTAG"
                            ? `Tạo "${search.title}"`
                            : search.title
                        }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-if="dataTag.length === 0 && isNotDraft"
          class="flex items-center justify-center py-4 text-center"
        >
          <div class="text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-8 h-8 mx-auto mb-2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"
              />
            </svg>
            <p class="text-sm">Chưa có tag nào</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from "vue";
import { useDebounceFn } from "@vueuse/core";

// Types
interface Tag {
  id: string;
  title: string;
}

interface SearchTag extends Tag {
  id: string;
}

// Props
const props = defineProps<{
  tag: {
    _id: string;
    description?: string;
  };
}>();

// Composables
const {
  updateConnectorDescription,
  searchTag,
  createTag,
  addTag,
  getTags,
  removeTag,
} = usePortal();
const orderStore = useOrderStore();
const route = useRoute();

// Refs
const inputRef = ref<HTMLInputElement | null>(null);
const inputKey = ref(0);

// Reactive state
const tagDescription = ref(props.tag.description || "");
const tagTitle = ref("");
const dataTag = ref<Tag[]>([]);
const dataSearchTag = ref<SearchTag[]>([]);
const isLoading = ref(false);
const isSearching = ref(false);

// Computed
const isNotDraft = computed(() => orderStore.isNotDraft);

// Methods
const fetchTags = async () => {
  if (!props.tag?._id) return;

  try {
    isLoading.value = true;
    const tags = await getTags(props.tag._id);
    dataTag.value = Array.isArray(tags) ? tags : [];
  } catch (error) {
    console.error("Error fetching tags:", error);
    dataTag.value = [];
  } finally {
    isLoading.value = false;
  }
};

const updateTagDescription = async () => {
  if (!props.tag?._id || isNotDraft.value) return;

  try {
    const auth = useCookie("auth").value as any;
    await updateConnectorDescription(
      props.tag._id,
      tagDescription.value,
      auth?.user?.id
    );
  } catch (error) {
    console.error("Error updating description:", error);
  }
};

const handleDescriptionInput = useDebounceFn(() => {
  if (tagDescription.value.length <= 500) {
    updateTagDescription();
  }
}, 1000);

const focusInput = () => {
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const clearInput = () => {
  tagTitle.value = "";
  dataSearchTag.value = [];
};

const saveTag = async (tag: Tag) => {
  if (!props.tag?._id) return;

  try {
    const auth = useCookie("auth").value as any;
    await addTag(props.tag._id, tag.title, tag.id, auth?.user?.id);
    await fetchTags();

    // Reset input
    nextTick(() => {
      inputKey.value++;
      tagTitle.value = "";
      dataSearchTag.value = [];
      focusInput();
    });
  } catch (error) {
    console.error("Error adding tag:", error);
  }
};

const createNewTag = async () => {
  if (!tagTitle.value.trim()) return;

  try {
    const auth = useCookie("auth").value as any;
    const newTag = await createTag(
      tagTitle.value.trim(),
      auth?.user?.id,
      route.query.orgId as string
    );

    if (newTag) {
      await saveTag({ id: newTag.id, title: tagTitle.value.trim() });
    }
  } catch (error) {
    console.error("Error creating tag:", error);
  }
};

const handleAddTag = async (tag: SearchTag) => {
  if (tag.id === "addTAG") {
    await createNewTag();
  } else {
    await saveTag(tag);
  }
};

const handleRemoveTag = async (tag: Tag) => {
  if (!props.tag?._id || isNotDraft.value) return;

  try {
    const auth = useCookie("auth").value as any;
    await removeTag(props.tag._id, tag.id, auth?.user?.id);
    await fetchTags();
  } catch (error) {
    console.error("Error removing tag:", error);
  }
};

const handleSearchTag = useDebounceFn(async (keyword: string) => {
  if (!keyword.trim()) {
    dataSearchTag.value = [];
    return;
  }

  try {
    isSearching.value = true;
    const trimmedKeyword = keyword.trim();

    const response = await searchTag("", "", "", trimmedKeyword);
    const searchResults = Array.isArray(response) ? response : [];

    // Check if exact match exists
    const hasExactMatch = searchResults.some(
      (tag) => tag.title.toLowerCase() === trimmedKeyword.toLowerCase()
    );

    // Add create option if no exact match
    dataSearchTag.value = hasExactMatch
      ? searchResults
      : [{ id: "addTAG", title: trimmedKeyword }, ...searchResults];
  } catch (error) {
    console.error("Error searching tags:", error);
    dataSearchTag.value = [];
  } finally {
    isSearching.value = false;
  }
}, 500);

const handleInputChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;

  // Handle Samsung keyboard issues
  if (window.navigator.userAgent.includes("Samsung")) {
    nextTick(() => {
      tagTitle.value = value;
    });
  }

  handleSearchTag(value);
};

const handleAddOrCreateTag = () => {
  const trimmedTitle = tagTitle.value.trim();
  if (!trimmedTitle) return;

  if (dataSearchTag.value.length === 0) {
    createNewTag();
  } else if (dataSearchTag.value.length === 1) {
    handleAddTag(dataSearchTag.value[0]);
  } else {
    // If multiple options, add the first one or create new if it's the "addTAG" option
    const firstOption = dataSearchTag.value[0];
    handleAddTag(firstOption);
  }
};

// Watchers
watch(
  () => props.tag.description,
  (newDescription) => {
    tagDescription.value = newDescription || "";
  }
);

// Lifecycle
onMounted(() => {
  fetchTags();
});
</script>
